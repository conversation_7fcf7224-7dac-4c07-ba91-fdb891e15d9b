import { faker } from "@faker-js/faker";
import { whitelistConfig } from "@wealthyhood/shared-configs";
import { ReferralCode, ReferralCodeDocument, LifetimeEnum } from "../../../models/ReferralCode";
import { User, UserDocument } from "../../../models/User";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildUser, buildParticipant, buildReferralCode } from "../../../tests/utils/generateModels";
import UserService from "../../userService";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";
import { Participant, ParticipantDocument } from "../../../models/Participant";

describe("UserService.setReferrerByCode", () => {
  beforeAll(async () => await connectDb("setReferrerByCode"));
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  describe("setReferrerByCode", () => {
    describe("when user is not referred and the code is expiring", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();

      let referrer: UserDocument;
      let user: UserDocument;
      let usedReferralCode: ReferralCodeDocument;
      let referrerParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ email: USER_EMAIL });
        await buildParticipant({ email: USER_EMAIL });

        // Build referrer
        referrer = await buildUser({ email: REFERRER_EMAIL });
        referrerParticipant = await buildParticipant({ email: REFERRER_EMAIL });

        usedReferralCode = await buildReferralCode({ owner: referrer.id, lifetime: LifetimeEnum.EXPIRING });

        await UserService.setReferrerByCode(user.id, `${usedReferralCode.code.toUpperCase()} `);
      });

      it("should update the user's referredByEmail & joinedWithCode property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(REFERRER_EMAIL);
        expect(updatedUser.joinedWithCode?.toString()).toBe(usedReferralCode.id);
      });

      it("should update the user's participant document with the referrer", async () => {
        const updatedParticipant = await Participant.findOne({ email: USER_EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(
          referrerParticipant.wlthdId
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.referralCodeSubmission.eventId,
          expect.objectContaining({ id: user.id }),
          { referredStatus: "True", joinedWithCode: usedReferralCode.code }
        );
      });

      it("should generate a new referral code", async () => {
        const referralCode = (await ReferralCode.findOne({
          owner: referrer.id,
          active: true
        })) as ReferralCodeDocument;
        expect(referralCode).not.toBeNull();
        expect(referralCode.code).not.toBe(usedReferralCode.code);
      });
    });

    describe("when user is not referred and the code is non-expiring", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();

      let referrer: UserDocument;
      let user: UserDocument;
      let usedReferralCode: ReferralCodeDocument;
      let referrerParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ email: USER_EMAIL });
        await buildParticipant({ email: USER_EMAIL });

        // Build referrer
        referrer = await buildUser({ email: REFERRER_EMAIL });
        referrerParticipant = await buildParticipant({ email: REFERRER_EMAIL });

        usedReferralCode = await buildReferralCode({ owner: referrer.id, lifetime: LifetimeEnum.NON_EXPIRING });

        await UserService.setReferrerByCode(user.id, `${usedReferralCode.code.toUpperCase()} `);
      });

      it("should update the user's referredByEmail & joinedWithCode property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(REFERRER_EMAIL);
        expect(updatedUser.joinedWithCode?.toString()).toBe(usedReferralCode.id);
      });

      it("should update the user's participant document with the referrer", async () => {
        const updatedParticipant = await Participant.findOne({ email: USER_EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(
          referrerParticipant.wlthdId
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.referralCodeSubmission.eventId,
          expect.objectContaining({ id: user.id }),
          { referredStatus: "True", joinedWithCode: usedReferralCode.code }
        );
      });

      it("should not generate a new referral code", async () => {
        const referralCodes = (await ReferralCode.find({
          owner: referrer.id,
          active: true
        })) as ReferralCodeDocument[];
        expect(referralCodes.length).toBe(1);

        const referralCode = referralCodes[0];
        expect(referralCode).not.toBeNull();
        expect(referralCode.code).toBe(usedReferralCode.code);
      });
    });

    describe("when user uses a whitelisted promo code for EU", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();

      let referrer: UserDocument;
      let user: UserDocument;
      let usedReferralCode: ReferralCodeDocument;
      let referrerParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ email: USER_EMAIL });
        await buildParticipant({ email: USER_EMAIL });

        // Build referrer
        referrer = await buildUser({ email: REFERRER_EMAIL });
        referrerParticipant = await buildParticipant({ email: REFERRER_EMAIL });

        usedReferralCode = await buildReferralCode({
          owner: referrer.id,
          lifetime: LifetimeEnum.NON_EXPIRING,
          code: whitelistConfig.WHITELIST_PROMO_CODES[0]
        });

        await UserService.setReferrerByCode(user.id, `${usedReferralCode.code.toUpperCase()} `);
      });

      it("should update the user's referredByEmail & joinedWithCode property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(REFERRER_EMAIL);
        expect(updatedUser.joinedWithCode?.toString()).toBe(usedReferralCode.id);
      });

      it("should update the user's participant document with the referrer", async () => {
        const updatedParticipant = await Participant.findOne({ email: USER_EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(
          referrerParticipant.wlthdId
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.referralCodeSubmission.eventId,
          expect.objectContaining({ id: user.id }),
          { referredStatus: "True", joinedWithCode: usedReferralCode.code }
        );
      });

      it("should not generate a new referral code", async () => {
        const referralCodes = (await ReferralCode.find({
          owner: referrer.id,
          active: true
        })) as ReferralCodeDocument[];
        expect(referralCodes.length).toBe(1);

        const referralCode = referralCodes[0];
        expect(referralCode).not.toBeNull();
        expect(referralCode.code).toBe(usedReferralCode.code);
      });

      it("should update the user's usedWhitelistCode property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.usedWhitelistCode).toBe(true);
      });

      it("should emit a 'usedWhitelistCode' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.usedWhitelistCode.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });
  });
});
