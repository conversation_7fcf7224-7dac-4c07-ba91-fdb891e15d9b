import { KycStatusEnum, User, UserDocument } from "../../../models/User";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildPortfolio,
  buildReward,
  buildUser
} from "../../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import UserService from "../../userService";
import { PortfolioModeEnum } from "../../../models/Portfolio";
import { CompanyEntityEnum } from "@wealthyhood/shared-configs/dist/entities";

describe("UserService.canUnlockFreeShare", () => {
  beforeAll(async () => await connectDb("canUnlockFreeShare"));
  afterAll(async () => await closeDb());
  afterEach(async () => await clearDb());

  describe("when user is not referred", () => {
    let user: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      user = await buildUser();
    });

    it("should return false", async () => {
      const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
      expect(canUnlockFreeShare).toBe(false);
    });
  });

  describe("WEALTHYHOOD_UK users", () => {
    describe("when user is referred but has signed up more than 10 days ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          createdAt: new Date("2022-07-29T11:00:00Z"),
          referredByEmail: faker.internet.email(),
          residencyCountry: "GB",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_UK
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today but has >= £100 investments", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GB",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 10000,
          consideration: {
            amount: 10000,
            currency: "GBP"
          }
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today, has <£100 investments but already has a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GB",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 9000,
          consideration: {
            amount: 9000,
            currency: "GBP"
          }
        });

        await buildReward({ targetUser: user.id });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today, has <£100 investments and does not have a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GB",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 9000,
          consideration: {
            amount: 9000,
            currency: "GBP"
          }
        });
      });

      it("should return true", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(true);
      });
    });
  });

  describe("WEALTHYHOOD_EUROPE users", () => {
    describe("when user is referred but is EU whitelisted", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GR",
          usedWhitelistCode: true, // This makes user EU whitelisted
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, not whitelisted, but has >= €100 deposits", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GR",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        await buildDepositCashTransaction({
          owner: user.id,
          status: "Settled",
          consideration: {
            amount: 10000, // €100 in cents
            currency: "EUR"
          }
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, not whitelisted, has <€100 deposits but already has a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GR",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        await buildDepositCashTransaction({
          owner: user.id,
          status: "Settled",
          consideration: {
            amount: 9000, // €90 in cents
            currency: "EUR"
          }
        });

        await buildReward({ targetUser: user.id });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, not whitelisted, has <€100 deposits and does not have a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: faker.internet.email(),
          residencyCountry: "GR",
          companyEntity: CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        await buildDepositCashTransaction({
          owner: user.id,
          status: "Settled",
          consideration: {
            amount: 9000, // €90 in cents
            currency: "EUR"
          }
        });
      });

      it("should return true", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(true);
      });
    });
  });
});
