import { Account } from "../models/Account";
import { BankAccount } from "../models/BankAccount";
import { Gift } from "../models/Gift";
import { PaymentMethod } from "../models/PaymentMethod";
import { Portfolio } from "../models/Portfolio";
import { Reward } from "../models/Reward";
import { RewardInvitation } from "../models/RewardInvitation";
import logger from "../external-services/loggerService";
import ScriptRunner from "../jobs/services/scriptRunner";
import { Address } from "../models/Address";
import { KycOperation } from "../models/KycOperation";
import { User, UserDocument } from "../models/User";

class RemoveCompanyEntity extends ScriptRunner {
  scriptName = "remove-company-entity";

  async processFn(): Promise<void> {
    logger.info("Removing company entity and related info from UK users who are KYC pending...", {
      module: `script:${this.scriptName}`
    });

    await User.find({
      residencyCountry: "GB",
      kycStatus: "pending",
      email: { $not: /^deleted_/ }
    })
      .populate("portfolios accounts")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users, index) => {
          logger.info(`Removing batch with index ${index}...`, {
            module: `script:${this.scriptName}`
          });

          const dbOperationPromises = users.map((owner: UserDocument) => {
            const userUpdate = User.findByIdAndUpdate(owner.id, {
              $unset: {
                companyEntity: 1,
                firstName: 1,
                lastName: 1,
                dateOfBirth: 1,
                submittedRequiredInfoAt: 1,
                activeProviders: 1,
                nationalities: 1,
                isPassportVerified: 1,
                isPotentiallyDuplicateAccount1: 1,
                providers: 1,
                currency: 1,
                employmentInfo: 1,
                taxResidency: 1,
                isUKTaxResident: 1,
                hasAcceptedTerms: 1,
                hasSeenBilling: 1,
                viewedWelcomePage: 1,
                viewedKYCSuccessPage: 1,
                viewedReferralCodeScreen: 1,
                viewedWealthybitesScreen: 1,
                joinedWaitingListAt: 1,
                kycFailedAt: 1,
                referredByEmail: 1,
                residencyCountry: 1
              },
              $set: {
                kycStatus: "pending",
                w8BenForm: {
                  activeProviders: []
                }
              }
            });
            const portfolioUpdate = Portfolio.findByIdAndUpdate(owner.portfolios[0].id, {
              $unset: {
                providers: 1,
                allocationCreationFlow: 1
              },
              $set: {
                initialHoldingsAllocation: []
              }
            });
            const accountUpdate = Account.findByIdAndUpdate(owner.accounts[0].id, {
              $unset: {
                providers: 1
              }
            });

            const addressDelete = Address.findOneAndDelete({ owner: owner.id });
            const kycOperationDelete = KycOperation.deleteMany({ owner: owner.id });
            const bankAccountDelete = BankAccount.deleteMany({ owner: owner.id });
            const giftDelete = Gift.deleteMany({ targetUserEmail: owner.email });
            const paymentMethodDelete = PaymentMethod.deleteMany({ owner: owner.id });
            const rewardInvitationDelete = RewardInvitation.deleteMany({ targetUserEmail: owner.email });
            const rewardDelete = Reward.deleteMany({ referral: owner.id });

            return [
              userUpdate,
              addressDelete,
              kycOperationDelete,
              portfolioUpdate,
              accountUpdate,
              bankAccountDelete,
              giftDelete,
              paymentMethodDelete,
              rewardInvitationDelete,
              rewardDelete
            ];
          });

          await Promise.all(dbOperationPromises.flat());
        },
        { batchSize: 10 }
      );

    logger.info("Removed company entity and related info from UK users who are KYC pending!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RemoveCompanyEntity().run();
